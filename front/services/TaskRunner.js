import {ConfigService} from "@/services/ConfigService";
import {ImageRecognitionService} from "@/services/ImageRecognitionService";
import {mobileLog, sleep} from "@/services/RobotArmService";
import {CountService} from "@/services/CountService";
import {TaskStatusSocket} from "@/services/websocket/TaskStatusSocket";
import {RobotArmSocket} from "@/services/websocket/RobotArmSocket";
import {ClickQueueService} from "@/services/ClickQueueService";

let taskRunner = null;

export class TaskRunner {
    constructor() {

        this.xIncrement = ConfigService.getXIncrement();  // X轴增量
        this.yFirstRow = ConfigService.getYFirstRow(); // 第一行Y轴增量
        this.ySecondRow = ConfigService.getYSecondRow();   // 第二行Y轴增量
        const maxMoveX = ConfigService.getMaxMoveX();  // 最大X轴移动距离
        //  最大X轴移动距离 是最大移动距离 减去 最后一屏拍照位置了
        this.maxMoveX = maxMoveX - (1280 / CountService.getPxWidth() - 10);

        this.currX = 0; // 当前X轴位置
        this.currY = 0; // 当前Y轴位置

        this.direction = 1; // 移动方向：1为向右，-1为向左
        this.currentRow = 1; // 当前行：1为第一行，2为第二行

        this.robotArmService = RobotArmSocket.getRobotArmService();
        this.imageRecognitionService = new ImageRecognitionService();

        // 点击队列服务
        this.clickQueueService = new ClickQueueService(this.robotArmService);


        // 任务状态管理
        this.isTaskRunning = false;
        this.taskStartTime = null;


    }


    static startTask() {
        if (taskRunner) {
            return;
        }
        taskRunner = new TaskRunner();

        // 初始化位置和状态
        taskRunner.currX = 0;
        taskRunner.currY = taskRunner.yFirstRow;
        taskRunner.direction = 1;
        taskRunner.currentRow = 1;


        mobileLog('任务开始执行', 'info');
        mobileLog(`配置参数: X增量=${this.xIncrement}, Y第一行=${this.yFirstRow}, Y第二行=${this.ySecondRow}`, 'info');

        taskRunner.runTask();
    }


    async runTask() {
        while (TaskStatusSocket.getStatus()) {

            // 并行执行：处理点击队列的同时准备下一步操作
            await this.clickQueueService.runClickQueue();

            // 移动到指定位置, 开始下一轮
            await this.moveToPosition();



            // 开始异步拍照识别（不等待完成）
            await this.captureAndStartRecognition();



            // 计算下一个位置
            this.countNextPos();


            // 智能等待：根据系统负载调整等待时间
            await this.smartWait();
        }

        mobileLog('任务循环结束', 'info');
    }




    /**
     * 处理任务错误
     */
    async handleTaskError(error) {
        const errorType = this.classifyError(error);

        switch (errorType) {
            case 'network':
                mobileLog('网络错误，等待网络恢复...', 'warning');
                await sleep(5000);
                break;

            case 'timeout':
                mobileLog('超时错误，减少并发操作...', 'warning');
                await sleep(2000);
                break;

            case 'resource':
                mobileLog('资源错误，执行清理操作...', 'warning');
                this.performMemoryCleanup();
                await sleep(3000);
                break;

            default:
                mobileLog('未知错误，标准恢复流程...', 'warning');
                await sleep(2000);
        }
    }

    /**
     * 错误分类
     */
    classifyError(error) {
        const message = error.message.toLowerCase();

        if (message.includes('network') || message.includes('连接') || message.includes('websocket')) {
            return 'network';
        }
        if (message.includes('timeout') || message.includes('超时')) {
            return 'timeout';
        }
        if (message.includes('memory') || message.includes('内存') || message.includes('resource')) {
            return 'resource';
        }

        return 'unknown';
    }

    /**
     * 检查是否应该执行内存清理
     */
    shouldPerformMemoryCleanup() {
        const timeSinceLastCleanup = Date.now() - this.lastMemoryCleanup;
        return timeSinceLastCleanup > this.memoryCleanupIntervalMs ||
            this.clickQueueService.getQueueLength() > 40;
    }

    /**
     * 移动到指定位置
     */
    async moveToPosition() {
        mobileLog(`移动到位置: X=${this.currX}, Y=${this.currY}, 第${this.currentRow}行`, 'info');
        await this.robotArmService.moveTo(this.currX, this.currY);
    }

    /**
     * 等待摄像头稳定并开始拍照识别（异步）
     */
    async captureAndStartRecognition() {
        // 等待摄像头画面稳定
        await sleep(600);

        // 异步拍照并识别，不等待识别结果
        // 拍照完成后立即返回，不阻塞机械臂移动
        this.startAsyncRecognition();

        // 立即返回，让机械臂可以继续移动到下一个位置
        mobileLog('拍照已启动，机械臂继续移动', 'info');
    }

    /**
     * 异步开始识别过程（改进版）
     */
    startAsyncRecognition() {
        // 记录当前机械臂坐标，用于后续计算点击位置
        const recognitionX = this.currX;
        const recognitionY = this.currY;
        const recognitionId = `recognition_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

        mobileLog(`识别过程已启动，记录识别位置: X=${recognitionX}, Y=${recognitionY}, ID=${recognitionId}`, 'info');

        // 使用超时控制的异步识别
        this.performAsyncRecognitionWithTimeout(recognitionX, recognitionY, recognitionId)
            .catch(error => {
                mobileLog(`识别过程失败 [${recognitionId}]: ${error.message}`, 'error');

            });
    }

    /**
     * 执行带超时控制的异步识别
     */
    async performAsyncRecognitionWithTimeout(recognitionX, recognitionY, recognitionId, timeoutMs = 15000) {
        return new Promise((resolve, reject) => {
            let isCompleted = false;

            // 设置超时
            const timeoutHandle = setTimeout(() => {
                if (!isCompleted) {
                    isCompleted = true;
                    reject(new Error(`识别超时 [${recognitionId}]`));
                }
            }, timeoutMs);

            try {
                // 执行识别
                this.imageRecognitionService.captureAndRecognize((recognitionResult) => {
                    if (isCompleted) {
                        mobileLog(`识别结果已过期，忽略 [${recognitionId}]`, 'warning');
                        return;
                    }

                    isCompleted = true;
                    clearTimeout(timeoutHandle);

                    try {
                        this.processRecognitionResult(recognitionResult, recognitionX, recognitionY, recognitionId);
                        resolve(recognitionResult);
                    } catch (error) {
                        reject(error);
                    }
                });
            } catch (error) {
                if (!isCompleted) {
                    isCompleted = true;
                    clearTimeout(timeoutHandle);
                    reject(error);
                }
            }
        });
    }

    /**
     * 处理识别结果
     */
    processRecognitionResult(recognitionResult, recognitionX, recognitionY, recognitionId) {
        try {
            if (recognitionResult && recognitionResult.results) {
                mobileLog(`处理识别结果 [${recognitionId}]: 发现 ${recognitionResult.results.length} 个目标`, 'info');

                for (let i = 0; i < recognitionResult.results.length; i++) {
                    // 将识别时的机械臂坐标附加到目标对象上
                    const target = recognitionResult.results[i];
                    target.recognitionX = recognitionX;
                    target.recognitionY = recognitionY;
                    target.recognitionId = recognitionId;
                    target.recognitionTime = Date.now();

                    this.clickQueueService.addToClickQueue(target);
                }
            } else {
                mobileLog(`识别结果为空 [${recognitionId}]`, 'info');
            }
        } catch (error) {
            mobileLog(`处理识别结果出错 [${recognitionId}]: ${error.message}`, 'error');
            throw error;
        }
    }


    countNextPos() {
        // 根据方向移动X轴
        this.currX += this.xIncrement * this.direction;

        // 检查是否到达边界
        if (this.direction === 1 && this.currX >= this.maxMoveX) {
            // 向右移动到达最大位置，切换到下一行
            this.currX = this.maxMoveX;
            mobileLog('到达右边界，准备切换到下一行', 'info');
            this.switchToNextRow();
        } else if (this.direction === -1 && this.currX <= 0) {
            // 向左移动到达最小位置，切换到下一行
            this.currX = 0;
            mobileLog('到达左边界，准备切换到下一行', 'info');
            this.switchToNextRow();
        }
    }

    switchToNextRow() {
        if (this.currentRow === 1) {
            // 从第一行切换到第二行
            this.currentRow = 2;
            this.currY = this.ySecondRow;
            this.direction = -1; // 第二行从右到左
            mobileLog('切换到第二行，方向：从右到左', 'info');
        } else {
            // 从第二行切换到第一行
            this.currentRow = 1;
            this.currY = this.yFirstRow;
            this.direction = 1; // 第一行从左到右
            mobileLog('切换到第一行，方向：从左到右', 'info');
        }
    }

    /**
     * 停止任务并清理资源
     */
    stopTask() {
        this.isTaskRunning = false;


        const runTime = this.taskStartTime ? Date.now() - this.taskStartTime : 0;
        mobileLog(`任务已停止，运行时间: ${Math.round(runTime / 1000)}秒`, 'info');


    }


}