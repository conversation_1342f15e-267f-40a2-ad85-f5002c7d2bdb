import {ConfigService} from "@/services/ConfigService";
import {mobileLog} from "@/services/RobotArmService";
import {CountService} from "@/services/CountService";


export class ClickQueueService {
    constructor(robotArmService) {
        this.robotArmService = robotArmService;

        // 点击队列配置
        this.clickQueue = [];
        this.maxQueueSize = 50; // 队列最大大小，防止内存泄漏

    }

    /**
     * 添加目标到点击队列
     * @param {Object} target 要点击的目标对象
     */
    add(target) {
        // 检查队列大小限制
        if (this.clickQueue.length >= this.maxQueueSize) {
            mobileLog(`队列已满(${this.maxQueueSize})，移除最旧的目标`, 'warning');
            this.clickQueue.shift(); // 移除最旧的目标
        }

        const bbox = target.bbox;
        const centerX = ((bbox[0] + bbox[2]) / 2).toFixed(2);
        const centerY = ((bbox[1] + bbox[3]) / 2).toFixed(2);

        mobileLog(`识别结果中心位置(像素坐标): (${centerX}, ${centerY})`, 'info');

        // 转换为机械臂坐标系
        const retX = (1280 - centerY) / CountService.getPxWidth();
        const retY = centerX / CountService.getPxWidth();

        // 获取识别时的机械臂位置
        const recognitionX = target.recognitionX;
        const recognitionY = target.recognitionY;

        // 计算真正的点击坐标：识别位置 + 识别结果的相对坐标
        const finalX = recognitionX + retX + ConfigService.getOffsetX();
        const finalY = recognitionY + retY + ConfigService.getOffsetY();

        mobileLog(`识别位置: (${recognitionX}, ${recognitionY}), 相对坐标: (${retX}, ${retY})`, 'info');
        mobileLog(`最终点击坐标: (${finalX}, ${finalY})`, 'info');

        // 改进的重复目标检测
        const isDuplicate = this._isDuplicateTarget(target, finalX, finalY);

        if (!isDuplicate) {
            // 将计算好的坐标添加到目标对象中
            target.finalX = finalX;
            target.finalY = finalY;
            target.addedTime = Date.now(); // 添加时间戳


            // 按优先级插入队列
            this.clickQueue.push(target);

            mobileLog(`目标已加入点击队列: ${target.class_name}, 坐标: (${finalX}, ${finalY}), 优先级: ${target.priority}, 当前队列长度: ${this.clickQueue.length}`, 'info');
        }
    }

    /**
     * 检查是否为重复目标（改进的算法）
     */
    _isDuplicateTarget(target, finalX, finalY) {
        const threshold = 5; // 距离阈值
        const timeThreshold = 10000; // 10秒内的目标认为可能重复
        const currentTime = Date.now();

        return this.clickQueue.some(existingTarget => {
            // 检查类型和时间
            if (existingTarget.class_name === target.class_name) {
                const timeDiff = currentTime - (existingTarget.addedTime || 0);

                // 如果是很久之前的目标，不认为是重复
                if (timeDiff > timeThreshold) {
                    return false;
                }

                // 计算距离
                const distance = Math.sqrt(
                    Math.pow(existingTarget.finalX - finalX, 2) +
                    Math.pow(existingTarget.finalY - finalY, 2)
                );

                if (distance < threshold) {
                    mobileLog(`检测到重复点击目标: ${target.class_name}, 距离: ${distance.toFixed(2)}, 时间差: ${timeDiff}ms, 跳过添加`, 'warning');
                    return true;
                }
            }
            return false;
        });
    }



    /**
     * 处理点击队列中的所有任务
     */
    async runClickQueue() {
        if (this.clickQueue.length === 0) {
            return;
        }

        const queueLength = this.clickQueue.length;
        mobileLog(`开始处理点击队列，共${queueLength}个目标`, 'info');

        while (this.clickQueue.length) {
            const target = this.clickQueue.shift();
            await this._handleTargetClick(target);
        }

    }


    /**
     * 处理目标点击逻辑
     * @param {Object} target 要点击的目标对象
     */
    async _handleTargetClick(target) {

        // 验证目标数据
        if (!target || !target.finalX || !target.finalY) {
            mobileLog('目标数据无效');
            return;
        }

        // 检查目标是否过期（超过30秒的识别结果可能已经不准确）
        if (target.addedTime && Date.now() - target.addedTime > 30000) {
            mobileLog(`目标已过期，跳过点击: ${target.class_name}`, 'warning');
            return;
        }

        // 检查机械臂服务是否可用
        if (!this.robotArmService) {
            mobileLog('机械臂服务不可用', 'error');
            return;
        }

        mobileLog(`开始点击目标: ${target.class_name}, 坐标: (${target.finalX}, ${target.finalY})`, 'info');

        // 执行点击操作
        await this.robotArmService.click(target.finalX, target.finalY);

    }


}
