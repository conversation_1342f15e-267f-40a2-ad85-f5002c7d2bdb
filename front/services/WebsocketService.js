import config from "../config.js";

/**
 * WebSocket服务组件
 * 提供稳定的长连接服务，支持自动重连、心跳检测和连接质量监控
 * 公共方法：init, close, on, send, off
 */
export class WebsocketService {
    constructor() {
        // WebSocket连接相关
        this._websocket = null;
        this._deviceId = '';
        this.connectionStatus = 'disconnected'; // disconnected, connecting, connected
        this._isManualClose = false;
        this._isConnecting = false;
        this._connectionPromise = null;

        // 事件监听器管理
        this._eventListeners = new Map();
        this._listenerCleanupQueue = new Set(); // 用于延迟清理监听器

        // 重连机制
        this._reconnectAttempts = 0;
        this._maxReconnectAttempts = 15; // 增加重连次数
        this._reconnectDelay = 2000; // 初始重连延迟
        this._maxReconnectDelay = 60000; // 最大重连延迟
        this._reconnectTimer = null;

        // 心跳检测
        this._heartbeatInterval = null;
        this._heartbeatIntervalMs = 25000; // 25秒心跳，避免30秒超时
        this._heartbeatTimeout = null;
        this._heartbeatTimeoutMs = 8000; // 8秒心跳超时
        this._lastHeartbeatTime = null;
        this._heartbeatFailureCount = 0;

        // 连接质量监控
        this._connectionQuality = {
            latency: 0,
            packetLoss: 0,
            lastPingTime: 0,
            consecutiveFailures: 0,
            totalReconnects: 0,
            connectionStartTime: null
        };

        // 消息处理优化
        this._messageQueue = [];
        this._isProcessingQueue = false;
        this._maxQueueSize = 100;

        // 资源清理
        this._cleanupTimer = null;
        this._memoryCleanupInterval = 300000; // 5分钟清理一次

        // 连接监控
        this._connectionMonitorTimer = null;
        this._connectionMonitorInterval = 60000; // 1分钟检查一次
        this._lastActivityTime = null;
        this._maxIdleTime = 300000; // 5分钟无活动时间

        // 性能统计
        this._performanceStats = {
            messagesSent: 0,
            messagesReceived: 0,
            bytesTransferred: 0,
            connectionUptime: 0,
            averageLatency: 0,
            latencyHistory: []
        };
    }

    // ==================== 公共方法 ====================

    /**
     * 初始化WebSocket连接
     * @param {string} deviceId - 设备ID（必填）
     * @returns {Promise<void>}
     */
    async init(deviceId) {
        if (!deviceId || typeof deviceId !== 'string') {
            throw new Error('设备ID必须是非空字符串');
        }

        this._deviceId = deviceId;
        this._isManualClose = false;

        // 启动内存清理定时器
        this._startMemoryCleanup();

        // 启动连接监控
        this._startConnectionMonitor();

        return this._connect();
    }

    /**
     * 监听事件
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     */
    on(eventType, callback) {
        if (!eventType || typeof callback !== 'function') {
            throw new Error('事件类型和回调函数都是必需的');
        }

        if (!this._eventListeners.has(eventType)) {
            this._eventListeners.set(eventType, new Set());
        }

        const listeners = this._eventListeners.get(eventType);
        listeners.add(callback);

        // 从清理队列中移除（如果存在）
        this._listenerCleanupQueue.delete(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数（可选，不传则移除该事件的所有监听器）
     */
    off(eventType, callback = null) {
        if (!this._eventListeners.has(eventType)) {
            return;
        }

        const listeners = this._eventListeners.get(eventType);

        if (callback) {
            listeners.delete(callback);
            // 延迟清理，避免在事件触发过程中立即删除
            this._listenerCleanupQueue.add(callback);
        } else {
            // 移除该事件类型的所有监听器
            listeners.clear();
        }

        // 如果没有监听器了，删除整个事件类型
        if (listeners.size === 0) {
            this._eventListeners.delete(eventType);
        }
    }

    /**
     * 发送消息到服务器
     * @param {string} eventName - 消息类型
     * @param {Object} jsonData - 消息对象
     * @param {Blob|ArrayBuffer} binaryData - 二进制数据（可选）
     * @returns {Promise<boolean>} 发送是否成功
     */
    async send(eventName, jsonData = {}, binaryData = null) {
        if (!eventName) {
            throw new Error('事件名称不能为空');
        }

        // 如果连接不可用，将消息加入队列
        if (!this._isConnectionReady()) {
            return this._queueMessage(eventName, jsonData, binaryData);
        }

        return this._sendMessage(eventName, jsonData, binaryData);
    }

    /**
     * 手动关闭连接
     */
    close() {
        this._isManualClose = true;
        this._cleanupAllResources();
        console.log('WebSocket连接已手动关闭');
    }

    // ==================== 私有方法 ====================

    /**
     * 建立WebSocket连接
     * @private
     */
    async _connect() {
        // 防止重复连接
        if (this._isConnecting) {
            console.log('连接正在进行中，等待现有连接完成');
            return this._connectionPromise;
        }

        if (this._websocket && this._websocket.readyState === WebSocket.OPEN) {
            return Promise.resolve();
        }

        this._isConnecting = true;
        this.connectionStatus = 'connecting';
        this._emit('connection_status_changed', {status: 'connecting'});

        this._connectionPromise = new Promise((resolve, reject) => {
            const protocol = config.useSSL ? 'wss' : 'ws';
            const wsUrl = `${protocol}://${config.host}:${config.port}/ws/${this._deviceId}`;
            console.log('正在连接WebSocket:', wsUrl);

            try {
                // 清理旧连接
                this._cleanupConnection();

                this._websocket = new WebSocket(wsUrl);
                this._setupWebSocketEvents(resolve, reject);

                // 连接超时处理
                const connectTimeout = setTimeout(() => {
                    if (this._websocket && this._websocket.readyState === WebSocket.CONNECTING) {
                        this._websocket.close();
                        reject(new Error('连接超时'));
                    }
                }, 15000); // 15秒连接超时

                this._websocket.addEventListener('open', () => {
                    clearTimeout(connectTimeout);
                });

            } catch (error) {
                console.error('WebSocket连接失败:', error);
                this.connectionStatus = 'disconnected';
                this._emit('connection_status_changed', {status: 'disconnected', error});
                this._isConnecting = false;
                this._scheduleReconnect();
                reject(error);
            }
        });

        try {
            await this._connectionPromise;
        } finally {
            this._isConnecting = false;
        }

        return this._connectionPromise;
    }

    /**
     * 解析 WebSocket 消息（优化内存使用）
     * @private
     * @param {MessageEvent<Blob>} event
     * @returns {Promise<{ json: object, blob: Blob | null }>}
     */
    async _parseWebSocketData(event) {
        try {
            const blob = event.data;
            if (blob.size === 0) return { json: {}, blob: null };

            // 读取前4字节获取头部长度
            const headerLengthBuffer = await blob.slice(0, 4).arrayBuffer();
            const headerLength = new DataView(headerLengthBuffer).getUint32(0, false);

            // 验证头部长度的合理性
            if (headerLength > blob.size - 4 || headerLength > 1024 * 1024) { // 限制头部最大1MB
                throw new Error('无效的消息格式：头部长度异常');
            }

            // 读取头部JSON
            const headerBlob = blob.slice(4, 4 + headerLength);
            const headerText = await headerBlob.text();
            const json = JSON.parse(headerText);

            // 获取二进制数据部分
            const binaryStart = 4 + headerLength;
            const payloadBlob = binaryStart < blob.size
                ? blob.slice(binaryStart)
                : null;

            return { json, blob: payloadBlob };
        } catch (error) {
            console.error('解析WebSocket消息失败:', error);
            throw error;
        }
    }

    /**
     * 设置WebSocket事件处理
     * @private
     */
    _setupWebSocketEvents(resolve, reject) {
        this._websocket.onopen = () => {
            this.connectionStatus = 'connected';
            this._reconnectAttempts = 0;
            this._connectionQuality.consecutiveFailures = 0;
            this._connectionQuality.connectionStartTime = Date.now();
            this._heartbeatFailureCount = 0;
            this._lastActivityTime = Date.now();

            console.log('WebSocket连接成功');

            // 启动心跳检测
            this._startHeartbeat();

            // 处理消息队列
            this._processMessageQueue();

            // 更新性能统计
            this._performanceStats.connectionUptime = 0;

            this._emit('connection_status_changed', {status: 'connected'});
            this._emit('connected', {deviceId: this._deviceId});

            if (resolve) resolve();
        };

        this._websocket.onmessage = async (event) => {
            try {
                const message = await this._parseWebSocketData(event);

                // 处理心跳响应
                if (message.json.eventName === 'pong') {
                    this._handleHeartbeatResponse(message.json.timestamp);
                    return;
                }

                // 重置连续失败计数
                this._connectionQuality.consecutiveFailures = 0;
                this._lastActivityTime = Date.now();

                // 更新性能统计
                this._performanceStats.messagesReceived++;
                if (message.blob) {
                    this._performanceStats.bytesTransferred += message.blob.size;
                }

                this._emit(message.json.eventName, message);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
                this._connectionQuality.consecutiveFailures++;

                // 如果解析失败次数过多，可能需要重连
                if (this._connectionQuality.consecutiveFailures >= 5) {
                    console.warn('消息解析连续失败，考虑重连');
                    this._websocket.close();
                }
            }
        };

        this._websocket.onclose = (event) => {
            this.connectionStatus = 'disconnected';
            console.log('WebSocket连接关闭', event);

            // 停止心跳检测
            this._stopHeartbeat();

            this._emit('connection_status_changed', {status: 'disconnected'});
            this._emit('disconnected', {code: event.code, reason: event.reason});

            // 如果不是手动关闭，则尝试重连
            if (!this._isManualClose) {
                this._connectionQuality.totalReconnects++;
                this._scheduleReconnect();
            }

            if (reject && event.code !== 1000) { // 1000 是正常关闭
                reject(new Error(`连接关闭: ${event.reason || event.code}`));
            }
        };

        this._websocket.onerror = (error) => {
            console.error('WebSocket错误详情:', error);
            this._connectionQuality.consecutiveFailures++;

            let errorMessage = 'WebSocket连接错误';
            if (error.currentTarget) {
                const readyState = error.currentTarget.readyState;
                if (readyState === WebSocket.CLOSED) {
                    errorMessage = 'WebSocket连接已关闭';
                } else if (readyState === WebSocket.CLOSING) {
                    errorMessage = 'WebSocket连接正在关闭';
                } else {
                    errorMessage = 'WebSocket连接失败：无法连接到服务器';
                }
            }

            this._emit('error', {error: errorMessage, originalError: error});

            if (reject) reject(new Error(errorMessage));
        };
    }

    /**
     * 安排重连
     * @private
     */
    _scheduleReconnect() {
        // 清除之前的重连定时器
        if (this._reconnectTimer) {
            clearTimeout(this._reconnectTimer);
            this._reconnectTimer = null;
        }

        if (this._reconnectAttempts >= this._maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            this._emit('reconnect_failed', {attempts: this._reconnectAttempts});
            return;
        }

        this._reconnectAttempts++;

        // 改进的指数退避算法，加入随机抖动
        const baseDelay = this._reconnectDelay * Math.pow(1.5, this._reconnectAttempts - 1);
        const jitter = Math.random() * 1000; // 0-1秒随机抖动
        const delay = Math.min(baseDelay + jitter, this._maxReconnectDelay);

        console.log(`${(delay / 1000).toFixed(1)}秒后尝试第${this._reconnectAttempts}次重连`);

        this._reconnectTimer = setTimeout(() => {
            if (this.connectionStatus === 'disconnected' &&
                !this._isManualClose &&
                !this._isConnecting) {
                this._emit('reconnecting', {attempt: this._reconnectAttempts});
                this._connect().catch(error => {
                    console.error('重连失败:', error);
                });
            }
        }, delay);
    }

    /**
     * 启动心跳检测
     * @private
     */
    _startHeartbeat() {
        this._stopHeartbeat(); // 先停止现有的心跳

        this._heartbeatInterval = setInterval(() => {
            this._sendHeartbeat();
        }, this._heartbeatIntervalMs);

        console.log('心跳检测已启动');
    }

    /**
     * 停止心跳检测
     * @private
     */
    _stopHeartbeat() {
        if (this._heartbeatInterval) {
            clearInterval(this._heartbeatInterval);
            this._heartbeatInterval = null;
        }

        if (this._heartbeatTimeout) {
            clearTimeout(this._heartbeatTimeout);
            this._heartbeatTimeout = null;
        }
    }

    /**
     * 发送心跳
     * @private
     */
    _sendHeartbeat() {
        if (!this._isConnectionReady()) {
            return;
        }

        const timestamp = Date.now();
        this._connectionQuality.lastPingTime = timestamp;

        // 直接发送，不通过队列
        this._sendMessage('ping', { timestamp }).catch(error => {
            console.warn('心跳发送失败:', error);
            this._heartbeatFailureCount++;
        });

        // 设置心跳超时
        this._heartbeatTimeout = setTimeout(() => {
            console.warn('心跳超时，连接可能已断开');
            this._heartbeatFailureCount++;
            this._connectionQuality.consecutiveFailures++;

            // 如果心跳连续失败次数过多，主动断开重连
            if (this._heartbeatFailureCount >= 3) {
                console.error('心跳连续失败，主动断开连接');
                if (this._websocket) {
                    this._websocket.close();
                }
            }
        }, this._heartbeatTimeoutMs);
    }

    /**
     * 处理心跳响应
     * @private
     */
    _handleHeartbeatResponse(timestamp) {
        if (this._heartbeatTimeout) {
            clearTimeout(this._heartbeatTimeout);
            this._heartbeatTimeout = null;
        }

        // 计算延迟
        const latency = Date.now() - timestamp;
        this._connectionQuality.latency = latency;
        this._connectionQuality.consecutiveFailures = 0;
        this._lastHeartbeatTime = Date.now();
        this._heartbeatFailureCount = 0;
        this._lastActivityTime = Date.now();

        // 更新延迟统计
        this._updateLatencyStats(latency);

        // 只在延迟异常时记录
        if (latency > 1000) {
            console.warn(`心跳响应延迟较高: ${latency}ms`);
        }
    }

    /**
     * 检查连接是否可用
     * @private
     */
    _isConnectionReady() {
        return this._websocket &&
               this._websocket.readyState === WebSocket.OPEN &&
               this.connectionStatus === 'connected';
    }

    /**
     * 将消息加入队列
     * @private
     */
    _queueMessage(eventName, jsonData, binaryData) {
        if (this._messageQueue.length >= this._maxQueueSize) {
            console.warn('消息队列已满，丢弃最旧的消息');
            this._messageQueue.shift();
        }

        this._messageQueue.push({ eventName, jsonData, binaryData, timestamp: Date.now() });
        return false; // 表示消息已入队，未立即发送
    }

    /**
     * 处理消息队列
     * @private
     */
    async _processMessageQueue() {
        if (this._isProcessingQueue || this._messageQueue.length === 0) {
            return;
        }

        this._isProcessingQueue = true;

        try {
            // 清理过期消息（超过30秒的消息）
            const now = Date.now();
            this._messageQueue = this._messageQueue.filter(msg =>
                now - msg.timestamp < 30000
            );

            // 发送队列中的消息
            while (this._messageQueue.length > 0 && this._isConnectionReady()) {
                const message = this._messageQueue.shift();
                await this._sendMessage(message.eventName, message.jsonData, message.binaryData);

                // 避免发送过快，稍微延迟
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        } catch (error) {
            console.error('处理消息队列失败:', error);
        } finally {
            this._isProcessingQueue = false;
        }
    }

    /**
     * 实际发送消息
     * @private
     */
    async _sendMessage(eventName, jsonData = {}, binaryData = null) {
        if (!this._isConnectionReady()) {
            throw new Error('WebSocket连接不可用');
        }

        try {
            // 准备头部 JSON
            const messageData = { ...jsonData };
            messageData.eventName = eventName;
            messageData.sendDeviceId = this._deviceId;
            messageData.timestamp = Date.now();

            const headerStr = JSON.stringify(messageData);
            const headerBuf = new TextEncoder().encode(headerStr);

            // 准备二进制数据
            let binaryBuf = new ArrayBuffer(0);
            if (binaryData instanceof Blob) {
                binaryBuf = await binaryData.arrayBuffer();
            } else if (binaryData instanceof ArrayBuffer) {
                binaryBuf = binaryData;
            }

            // 构造完整消息
            const headerLength = headerBuf.length;
            const message = new Uint8Array(4 + headerLength + binaryBuf.byteLength);

            // 写入头长度（大端序）
            new DataView(message.buffer).setUint32(0, headerLength, false);

            // 写入头部和二进制数据
            message.set(headerBuf, 4);
            if (binaryBuf.byteLength > 0) {
                message.set(new Uint8Array(binaryBuf), 4 + headerLength);
            }

            // 发送消息
            this._websocket.send(message);

            // 更新性能统计
            this._performanceStats.messagesSent++;
            this._performanceStats.bytesTransferred += message.byteLength;
            this._lastActivityTime = Date.now();

            return true;
        } catch (error) {
            console.error('发送消息失败:', error);
            this._emit('send_failed', {reason: 'send_error', error: error.message});
            throw error;
        }
    }

    /**
     * 触发事件
     * @private
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    _emit(eventType, data = {}) {
        if (!this._eventListeners.has(eventType)) {
            return;
        }

        const listeners = this._eventListeners.get(eventType);

        // 使用 Array.from 创建副本，避免在迭代过程中修改原数组
        const listenersCopy = Array.from(listeners);

        listenersCopy.forEach(callback => {
            try {
                // 检查回调是否还在监听器列表中（可能在执行过程中被移除）
                if (listeners.has(callback)) {
                    callback(data);
                }
            } catch (error) {
                console.error(`事件监听器执行错误 (${eventType}):`, error);
                // 移除有问题的监听器
                listeners.delete(callback);
            }
        });

        // 清理已标记删除的监听器
        this._cleanupListeners();
    }

    /**
     * 清理已标记删除的监听器
     * @private
     */
    _cleanupListeners() {
        this._listenerCleanupQueue.forEach(callback => {
            this._eventListeners.forEach(listeners => {
                listeners.delete(callback);
            });
        });
        this._listenerCleanupQueue.clear();
    }

    /**
     * 清理连接资源
     * @private
     */
    _cleanupConnection() {
        this._stopHeartbeat();

        if (this._websocket) {
            // 移除所有事件监听器
            this._websocket.onopen = null;
            this._websocket.onmessage = null;
            this._websocket.onclose = null;
            this._websocket.onerror = null;

            // 如果连接还在，关闭它
            if (this._websocket.readyState === WebSocket.OPEN ||
                this._websocket.readyState === WebSocket.CONNECTING) {
                this._websocket.close();
            }

            this._websocket = null;
        }
    }

    /**
     * 清理所有资源
     * @private
     */
    _cleanupAllResources() {
        // 清理连接
        this._cleanupConnection();

        // 清理定时器
        if (this._reconnectTimer) {
            clearTimeout(this._reconnectTimer);
            this._reconnectTimer = null;
        }

        if (this._cleanupTimer) {
            clearInterval(this._cleanupTimer);
            this._cleanupTimer = null;
        }

        if (this._connectionMonitorTimer) {
            clearInterval(this._connectionMonitorTimer);
            this._connectionMonitorTimer = null;
        }

        // 清理状态
        this.connectionStatus = 'disconnected';
        this._reconnectAttempts = 0;
        this._isConnecting = false;
        this._connectionPromise = null;
        this._isProcessingQueue = false;

        // 清理消息队列
        this._messageQueue.length = 0;

        // 清理事件监听器
        this._eventListeners.clear();
        this._listenerCleanupQueue.clear();

        // 重置连接质量
        this._connectionQuality = {
            latency: 0,
            packetLoss: 0,
            lastPingTime: 0,
            consecutiveFailures: 0,
            totalReconnects: 0,
            connectionStartTime: null
        };

        // 重置性能统计
        this._performanceStats = {
            messagesSent: 0,
            messagesReceived: 0,
            bytesTransferred: 0,
            connectionUptime: 0,
            averageLatency: 0,
            latencyHistory: []
        };
    }

    /**
     * 启动内存清理
     * @private
     */
    _startMemoryCleanup() {
        if (this._cleanupTimer) {
            clearInterval(this._cleanupTimer);
        }

        this._cleanupTimer = setInterval(() => {
            this._performMemoryCleanup();
        }, this._memoryCleanupInterval);
    }

    /**
     * 执行内存清理
     * @private
     */
    _performMemoryCleanup() {
        try {
            // 清理过期的消息队列
            const now = Date.now();
            this._messageQueue = this._messageQueue.filter(msg =>
                now - msg.timestamp < 60000 // 保留1分钟内的消息
            );

            // 清理监听器
            this._cleanupListeners();

            // 清理延迟历史记录（只保留最近100条）
            if (this._performanceStats.latencyHistory.length > 100) {
                this._performanceStats.latencyHistory = this._performanceStats.latencyHistory.slice(-100);
            }

            // 如果连接长时间断开，重置一些状态
            if (this.connectionStatus === 'disconnected' &&
                this._reconnectAttempts > 5) {
                console.log('执行深度清理，重置连接状态');
                this._reconnectAttempts = Math.floor(this._reconnectAttempts / 2);
                this._connectionQuality.consecutiveFailures = 0;
            }

            // 强制垃圾回收提示（如果浏览器支持）
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }
        } catch (error) {
            console.error('内存清理失败:', error);
        }
    }

    /**
     * 启动连接监控
     * @private
     */
    _startConnectionMonitor() {
        if (this._connectionMonitorTimer) {
            clearInterval(this._connectionMonitorTimer);
        }

        this._connectionMonitorTimer = setInterval(() => {
            this._performConnectionCheck();
        }, this._connectionMonitorInterval);
    }

    /**
     * 执行连接检查
     * @private
     */
    _performConnectionCheck() {
        try {
            const now = Date.now();

            // 更新连接时长
            if (this._connectionQuality.connectionStartTime) {
                this._performanceStats.connectionUptime = now - this._connectionQuality.connectionStartTime;
            }

            // 检查空闲时间
            if (this._lastActivityTime &&
                now - this._lastActivityTime > this._maxIdleTime &&
                this.connectionStatus === 'connected') {
                console.log('连接空闲时间过长，发送保活消息');
                this._sendMessage('keepalive', { timestamp: now }).catch(error => {
                    console.warn('保活消息发送失败:', error);
                });
            }

            // 检查连接健康状态
            if (!this.isConnectionHealthy() && this.connectionStatus === 'connected') {
                console.warn('检测到连接不健康，主动重连');
                if (this._websocket) {
                    this._websocket.close();
                }
            }

            // 发送连接状态事件
            this._emit('connection_monitor', {
                quality: this.getConnectionQuality(),
                performance: this.getPerformanceStats(),
                isHealthy: this.isConnectionHealthy()
            });

        } catch (error) {
            console.error('连接检查失败:', error);
        }
    }

    /**
     * 更新延迟统计
     * @private
     */
    _updateLatencyStats(latency) {
        this._performanceStats.latencyHistory.push({
            timestamp: Date.now(),
            latency: latency
        });

        // 计算平均延迟（最近10次）
        const recentLatencies = this._performanceStats.latencyHistory.slice(-10);
        const avgLatency = recentLatencies.reduce((sum, item) => sum + item.latency, 0) / recentLatencies.length;
        this._performanceStats.averageLatency = Math.round(avgLatency);
    }

    // ==================== 调试和监控方法 ====================

    /**
     * 获取连接质量信息
     * @returns {Object} 连接质量信息
     */
    getConnectionQuality() {
        return {
            ...this._connectionQuality,
            status: this.connectionStatus,
            reconnectAttempts: this._reconnectAttempts,
            lastHeartbeat: this._lastHeartbeatTime,
            messageQueueSize: this._messageQueue.length,
            listenerCount: this._getListenerCount(),
            uptime: this._connectionQuality.connectionStartTime
                ? Date.now() - this._connectionQuality.connectionStartTime
                : 0
        };
    }

    /**
     * 检查连接健康状态
     * @returns {boolean} 连接是否健康
     */
    isConnectionHealthy() {
        if (this.connectionStatus !== 'connected') {
            return false;
        }

        // 检查心跳是否正常
        if (this._lastHeartbeatTime) {
            const timeSinceLastHeartbeat = Date.now() - this._lastHeartbeatTime;
            if (timeSinceLastHeartbeat > this._heartbeatIntervalMs * 2.5) {
                return false;
            }
        }

        // 检查连续失败次数和心跳失败次数
        return this._connectionQuality.consecutiveFailures < 3 &&
               this._heartbeatFailureCount < 2;
    }

    /**
     * 获取监听器总数
     * @private
     */
    _getListenerCount() {
        let count = 0;
        this._eventListeners.forEach(listeners => {
            count += listeners.size;
        });
        return count;
    }


    /**
     * 获取性能统计信息
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        return {
            ...this._performanceStats,
            connectionUptimeFormatted: this._formatUptime(this._performanceStats.connectionUptime),
            throughput: this._calculateThroughput()
        };
    }

    /**
     * 强制重连（调试用）
     * @returns {Promise<void>}
     */
    forceReconnect() {
        if (this._websocket) {
            this._websocket.close();
        }
        return this._connect();
    }

    /**
     * 格式化运行时间
     * @private
     */
    _formatUptime(uptime) {
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟 ${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 计算吞吐量
     * @private
     */
    _calculateThroughput() {
        const uptimeSeconds = this._performanceStats.connectionUptime / 1000;
        if (uptimeSeconds === 0) return { messagesPerSecond: 0, bytesPerSecond: 0 };

        return {
            messagesPerSecond: (this._performanceStats.messagesSent + this._performanceStats.messagesReceived) / uptimeSeconds,
            bytesPerSecond: this._performanceStats.bytesTransferred / uptimeSeconds
        };
    }
}
